import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import analyticsService from "../services/analytics.service";
import { sequelize } from "../models/index";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "../helper/transaction.helper";
import { Val<PERSON><PERSON><PERSON><PERSON>per } from "../helper/validation.helper";
import analyticsController from "./analytics.controller";

/**
 * Get dashboard overview statistics - ROBUST VERSION
 * @route GET /api/v1/private/dashboard/overview
 * @access Private (Authenticated users)
 */
const getDashboardOverview = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);
    const { date_range = "last_30_days", category_type } = sanitizedQuery;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        sanitizedQuery.organization_id
      );

    // Validate date_range parameter
    const validDateRanges = [
      "last_7_days",
      "last_30_days",
      "last_90_days",
      "custom",
    ];
    if (!validDateRanges.includes(date_range)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message:
          "Invalid date_range. Must be one of: " + validDateRanges.join(", "),
      });
    }

    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error("Analytics service timeout")), 10000); // 10 second timeout
    });

    // Get dashboard stats from analytics service with timeout
    const stats: any = await Promise.race([
      analyticsService.getDashboardStats(
        effectiveOrganizationId,
        date_range as string
      ),
      timeoutPromise,
    ]);

    const overviewData = {
      // Core Statistics - Dashboard Cards (matching your UI design)
      stats: {
        totalRecipes: stats.totalRecipes,
        activeUsers: stats.activeUsers,
        topCategory: stats.topCategory,
        highestImpressionRecipe: stats.highestImpressionRecipe,
      },

      // Analytics Statistics
      analytics: {
        totalViews: stats.totalViews,
        totalContactSubmissions: stats.totalContactSubmissions,
      },

      // Dashboard Charts Data (matching your UI design)
      charts: {
        recipeViewsTrend: stats.recipeViewsTrend, // Line chart - Top 10 recipes with single names
        categoryPerformance: stats.categoryPerformance, // Bar chart
        userEngagementHeatmap: stats.userEngagementHeatmap, // Heatmap - NEW!
        conversionFunnel: stats.conversionFunnel, // Conversion analytics - NEW!
      },

      // Recent Activity
      recentActivity: stats.recentActivity,
    };

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Dashboard overview fetched successfully",
      data: overviewData,
      timestamp: new Date().toISOString(),
    });
  } catch (error: any) {
    console.error("Dashboard overview error:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: "Failed to fetch dashboard overview",
      error:
        process.env.NODE_ENV === "development"
          ? error.message
          : "Internal server error",
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Get system health status
 * @route GET /api/v1/private/dashboard/health
 */
const getSystemHealth = async (_req: Request, res: Response): Promise<any> => {
  try {
    const healthChecks = {
      database: false,
      storage: false,
      analytics: false,
      overall: false,
    };

    // Check database connection
    try {
      await sequelize.authenticate();
      healthChecks.database = true;
    } catch (error) {
      console.error("Database health check failed:", error);
    }

    // Check storage (basic check)
    try {
      // This would check your file storage system
      healthChecks.storage = true;
    } catch (error) {
      console.error("Storage health check failed:", error);
    }

    // Check analytics
    try {
      await analyticsService.getAnalytics({ limit: 1 });
      healthChecks.analytics = true;
    } catch (error) {
      console.error("Analytics health check failed:", error);
    }

    // Overall health
    healthChecks.overall =
      healthChecks.database && healthChecks.storage && healthChecks.analytics;

    const status = healthChecks.overall
      ? StatusCodes.OK
      : StatusCodes.SERVICE_UNAVAILABLE;

    return res.status(status).json({
      status: healthChecks.overall,
      message: healthChecks.overall ? "System is healthy" : "System has issues",
      data: {
        checks: healthChecks,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error: any) {
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: "Health check failed",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

/**
 * Export dashboard data - ENHANCED VERSION WITH TIMEOUT AND BETTER ERROR HANDLING
 * @route GET /api/v1/private/dashboard/export
 */
const exportDashboardData = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);
    const { format = "json", date_range = "last_30_days" } = sanitizedQuery;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        sanitizedQuery.organization_id
      );

    // Add timeout to prevent hanging (same as dashboard overview)
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error("Export service timeout")), 15000); // 15 second timeout for export
    });

    // Get dashboard stats with timeout protection
    const dashboardData = await Promise.race([
      analyticsService.getDashboardStats(
        effectiveOrganizationId,
        date_range as string
      ),
      timeoutPromise,
    ]);

    if (format === "csv") {
      // Convert to CSV format with corrected data mapping
      const csv = convertToCSV(dashboardData, date_range);
      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=dashboard-export-${new Date().toISOString().split("T")[0]}.csv`
      );
      return res.send(csv);
    }

    // Default JSON export
    res.setHeader("Content-Type", "application/json");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=dashboard-export-${new Date().toISOString().split("T")[0]}.json`
    );

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("DASHBOARD_DATA_EXPORTED_SUCCESSFULLY"),
      data: dashboardData,
      exportedAt: new Date().toISOString(),
      dateRange: date_range,
      organizationId: effectiveOrganizationId,
    });
  } catch (error: any) {
    console.error("Dashboard export error:", error);

    // Handle specific error types
    if (error.message === "Export service timeout") {
      return res.status(StatusCodes.REQUEST_TIMEOUT).json({
        status: false,
        message:
          "Export request timed out. Please try again or contact support.",
        errorType: "TIMEOUT_ERROR",
      });
    }

    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error exporting dashboard data"
    );
  }
};

// Helper function to convert data to CSV - CORRECTED VERSION
function convertToCSV(data: any, dateRange: string = "last_30_days"): string {
  const headers = ["Metric", "Value"];

  // Use the actual data structure returned by getDashboardStats
  const rows = [
    // Export metadata
    ["Export Date", new Date().toISOString()],
    ["Date Range", dateRange],
    ["", ""], // Empty row for separation

    // Core Business Metrics
    ["Total Recipes", data.totalRecipes || 0],
    ["Top Category Name", data.topCategory?.name || "No Data"],
    ["Top Category Count", data.topCategory?.count || 0],
    ["", ""], // Empty row for separation

    // Recipe Analytics
    ["Total Views", data.totalViews || 0],
    ["Total Bookmarks", data.totalBookmarks || 0],
    ["Total Shares", data.totalShares || 0],
    ["Total Contact Submissions", data.totalContactSubmissions || 0],
    ["", ""], // Empty row for separation

    // Chart Data Summary
    ["Recipe Views Trend Data Points", data.recipeViewsTrend?.length || 0],
    ["Category Performance Data Points", data.categoryPerformance?.length || 0],
    [
      "User Engagement Heatmap Data Points",
      data.userEngagementHeatmap?.length || 0,
    ],
    ["Conversion Funnel Data Points", data.conversionFunnel?.length || 0],
    ["Recent Activity Items", data.recentActivity?.length || 0],
  ];

  const csvContent = [
    `# Dashboard Export - ${new Date().toISOString()}`,
    `# Date Range: ${dateRange}`,
    `# Generated by Recipe Management System`,
    "",
    headers.join(","),
    ...rows.map((row) => row.join(",")),
  ].join("\n");

  return csvContent;
}

/**
 * Get CTA analytics for dashboard - Wrapper for analytics controller
 * @route GET /api/v1/private/dashboard/cta-analytics
 */
const getCtaAnalytics = async (req: any, res: Response): Promise<any> => {
  // Reuse existing analytics controller logic
  return analyticsController.getCtaClickAnalytics(req, res);
};

/**
 * Get contact analytics for dashboard - Wrapper for analytics controller
 * @route GET /api/v1/private/dashboard/contact-analytics
 */
const getContactAnalytics = async (req: any, res: Response): Promise<any> => {
  // Reuse existing analytics controller logic
  return analyticsController.getContactSubmissionAnalytics(req, res);
};

export default {
  getDashboardOverview,
  getSystemHealth,
  exportDashboardData,
  getCtaAnalytics,
  getContactAnalytics,
};
