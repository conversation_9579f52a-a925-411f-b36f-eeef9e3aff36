import request from 'supertest';
import express from 'express';
import { validateBatchRequest } from '../middleware/batch-validator';

const app = express();
app.use(express.json());

// Test routes for each batch type
app.post('/test/basic-info', validateBatchRequest('basic-info'), (req, res) => {
  res.json({ success: true });
});

app.post('/test/ingredients-nutrition', validateBatchRequest('ingredients-nutrition'), (req, res) => {
  res.json({ success: true });
});

app.post('/test/steps', validateBatchRequest('steps'), (req, res) => {
  res.json({ success: true });
});

app.post('/test/uploads', validateBatchRequest('uploads'), (req, res) => {
  res.json({ success: true });
});

describe('Batch Validator Tests', () => {
  describe('Basic Info Validation', () => {
    it('should pass with valid basic info data', async () => {
      const validData = {
        recipe_title: 'Test Recipe',
        recipe_description: 'A test recipe',
        recipe_preparation_time: 30,
        has_recipe_public_visibility: true
      };

      const response = await request(app)
        .post('/test/basic-info')
        .send(validData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it('should fail without recipe_title', async () => {
      const invalidData = {
        recipe_description: 'A test recipe'
      };

      const response = await request(app)
        .post('/test/basic-info')
        .send(invalidData);

      expect(response.status).toBe(400);
    });

    it('should fail with recipe_title exceeding 100 characters', async () => {
      const invalidData = {
        recipe_title: 'A'.repeat(101)
      };

      const response = await request(app)
        .post('/test/basic-info')
        .send(invalidData);

      expect(response.status).toBe(400);
    });
  });

  describe('Ingredients Nutrition Validation', () => {
    it('should pass with valid ingredients nutrition data', async () => {
      const validData = {
        recipe_id: 123,
        ingredients: [{ name: 'flour', quantity: 100 }],
        nutrition_attributes: [1, 2, 3]
      };

      const response = await request(app)
        .post('/test/ingredients-nutrition')
        .send(validData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it('should fail without recipe_id', async () => {
      const invalidData = {
        ingredients: [{ name: 'flour', quantity: 100 }]
      };

      const response = await request(app)
        .post('/test/ingredients-nutrition')
        .send(invalidData);

      expect(response.status).toBe(400);
    });

    it('should fail with invalid recipe_id', async () => {
      const invalidData = {
        recipe_id: 'invalid',
        ingredients: [{ name: 'flour', quantity: 100 }]
      };

      const response = await request(app)
        .post('/test/ingredients-nutrition')
        .send(invalidData);

      expect(response.status).toBe(400);
    });
  });

  describe('Steps Validation', () => {
    it('should pass with valid steps data', async () => {
      const validData = {
        recipe_id: 123,
        steps: [{ step_number: 1, description: 'Mix ingredients' }],
        batch_number: 1,
        is_final_batch: true
      };

      const response = await request(app)
        .post('/test/steps')
        .send(validData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it('should fail without steps array', async () => {
      const invalidData = {
        recipe_id: 123,
        batch_number: 1,
        is_final_batch: true
      };

      const response = await request(app)
        .post('/test/steps')
        .send(invalidData);

      expect(response.status).toBe(400);
    });

    it('should fail with empty steps array', async () => {
      const invalidData = {
        recipe_id: 123,
        steps: [],
        batch_number: 1,
        is_final_batch: true
      };

      const response = await request(app)
        .post('/test/steps')
        .send(invalidData);

      expect(response.status).toBe(400);
    });

    it('should fail without is_final_batch', async () => {
      const invalidData = {
        recipe_id: 123,
        steps: [{ step_number: 1, description: 'Mix ingredients' }],
        batch_number: 1
      };

      const response = await request(app)
        .post('/test/steps')
        .send(invalidData);

      expect(response.status).toBe(400);
    });
  });

  describe('Uploads Validation', () => {
    it('should pass with valid uploads data', async () => {
      const validData = {
        recipe_id: 123,
        batch_number: 1,
        is_final_batch: false
      };

      const response = await request(app)
        .post('/test/uploads')
        .send(validData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it('should fail without recipe_id', async () => {
      const invalidData = {
        batch_number: 1,
        is_final_batch: false
      };

      const response = await request(app)
        .post('/test/uploads')
        .send(invalidData);

      expect(response.status).toBe(400);
    });

    it('should fail without batch_number', async () => {
      const invalidData = {
        recipe_id: 123,
        is_final_batch: false
      };

      const response = await request(app)
        .post('/test/uploads')
        .send(invalidData);

      expect(response.status).toBe(400);
    });
  });

  describe('Invalid Batch Type', () => {
    it('should throw error for unknown batch type', () => {
      expect(() => {
        validateBatchRequest('unknown-type');
      }).toThrow('Unknown batch request type: unknown-type');
    });
  });
});
