import { celebrate, Joi, Segments } from "celebrate";

/**
 * Validate basic recipe information batch request
 */
const validateBasicInfoBatch = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        recipe_title: Joi.string().max(100).required().messages({
          "string.max": "Recipe title cannot exceed 100 characters",
          "any.required": "Recipe title is required",
        }),
        recipe_public_title: Joi.string().max(100).allow(null, "").optional().messages({
          "string.max": "Recipe public title cannot exceed 100 characters",
        }),
        recipe_description: Joi.string().allow(null, "").optional(),
        recipe_preparation_time: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d+$/))
          .allow(null)
          .optional()
          .messages({
            "number.min": "Preparation time cannot be negative",
            "string.pattern.base": "Preparation time must be a valid number",
          }),
        recipe_cook_time: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d+$/))
          .allow(null)
          .optional()
          .messages({
            "number.min": "Cook time cannot be negative",
            "string.pattern.base": "Cook time must be a valid number",
          }),
        has_recipe_public_visibility: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .default(false)
          .messages({
            "alternatives.match": "Public visibility must be a boolean (true/false) or string ('true'/'false')",
          }),
        has_recipe_private_visibility: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .default(false)
          .messages({
            "alternatives.match": "Private visibility must be a boolean (true/false) or string ('true'/'false')",
          }),
        recipe_status: Joi.string().valid("draft", "published", "archived").optional(),
        recipe_complexity_level: Joi.string().valid("easy", "medium", "hard").optional(),
        categories: Joi.alternatives()
          .try(
            Joi.array().items(Joi.number().positive()),
            Joi.string()
          )
          .optional(),
        dietary_attributes: Joi.alternatives()
          .try(
            Joi.array().items(Joi.number().positive()),
            Joi.string()
          )
          .optional(),
      })
      .unknown(true), // Allow additional fields for flexibility
  });

/**
 * Validate ingredients and nutrition batch request
 */
const validateIngredientsNutritionBatch = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        recipe_id: Joi.alternatives()
          .try(Joi.number().positive(), Joi.string().pattern(/^\d+$/))
          .required()
          .messages({
            "number.positive": "Recipe ID must be a positive number",
            "string.pattern.base": "Recipe ID must be a valid number",
            "any.required": "Recipe ID is required",
          }),
        ingredients: Joi.array().items(Joi.object()).optional(),
        nutrition_attributes: Joi.alternatives()
          .try(
            Joi.array().items(Joi.number().positive()),
            Joi.string()
          )
          .optional(),
        allergen_attributes: Joi.alternatives()
          .try(
            Joi.array().items(Joi.number().positive()),
            Joi.string()
          )
          .optional(),
        cuisine_attributes: Joi.alternatives()
          .try(
            Joi.array().items(Joi.number().positive()),
            Joi.string()
          )
          .optional(),
        dietary_attributes: Joi.alternatives()
          .try(
            Joi.array().items(Joi.number().positive()),
            Joi.string()
          )
          .optional(),
        haccp_attributes: Joi.alternatives()
          .try(
            Joi.array().items(Joi.number().positive()),
            Joi.string()
          )
          .optional(),
        is_ingredient_cooking_method: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .optional(),
        is_preparation_method: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .optional(),
        is_cost_manual: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .optional(),
        // Serving details
        recipe_serve_in: Joi.string().allow(null, "").optional(),
        recipe_garnish: Joi.string().allow(null, "").optional(),
        recipe_head_chef_tips: Joi.string().allow(null, "").optional(),
        recipe_foh_tips: Joi.string().allow(null, "").optional(),
        recipe_impression: Joi.string().allow(null, "").optional(),
        recipe_yield: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d+(\.\d+)?$/))
          .allow(null)
          .optional(),
        recipe_yield_unit: Joi.alternatives()
          .try(Joi.number().positive(), Joi.string().pattern(/^\d+$/))
          .allow(null)
          .optional(),
        recipe_total_portions: Joi.alternatives()
          .try(Joi.number().min(1), Joi.string().pattern(/^\d+$/))
          .allow(null)
          .optional(),
        recipe_single_portion_size: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d+(\.\d+)?$/))
          .allow(null)
          .optional(),
        recipe_serving_method: Joi.string().allow(null, "").optional(),
      })
      .unknown(true), // Allow additional fields for flexibility
  });

/**
 * Validate recipe steps batch request
 */
const validateStepsBatch = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        recipe_id: Joi.alternatives()
          .try(Joi.number().positive(), Joi.string().pattern(/^\d+$/))
          .required()
          .messages({
            "number.positive": "Recipe ID must be a positive number",
            "string.pattern.base": "Recipe ID must be a valid number",
            "any.required": "Recipe ID is required",
          }),
        steps: Joi.array()
          .items(Joi.object())
          .min(1)
          .required()
          .messages({
            "array.min": "At least one step is required",
            "any.required": "Steps must be a valid array",
          }),
        batch_number: Joi.alternatives()
          .try(Joi.number().positive(), Joi.string().pattern(/^\d+$/))
          .required()
          .messages({
            "number.positive": "Batch number must be a positive number",
            "string.pattern.base": "Batch number must be a valid number",
            "any.required": "Batch number is required",
          }),
        is_final_batch: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .required()
          .messages({
            "alternatives.match": "is_final_batch must be a boolean (true/false) or string ('true'/'false')",
            "any.required": "is_final_batch flag is required",
          }),
      })
      .unknown(true), // Allow additional fields for flexibility
  });

/**
 * Validate recipe uploads batch request
 */
const validateUploadsBatch = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        recipe_id: Joi.alternatives()
          .try(Joi.number().positive(), Joi.string().pattern(/^\d+$/))
          .required()
          .messages({
            "number.positive": "Recipe ID must be a positive number",
            "string.pattern.base": "Recipe ID must be a valid number",
            "any.required": "Recipe ID is required",
          }),
        batch_number: Joi.alternatives()
          .try(Joi.number().positive(), Joi.string().pattern(/^\d+$/))
          .required()
          .messages({
            "number.positive": "Batch number must be a positive number",
            "string.pattern.base": "Batch number must be a valid number",
            "any.required": "Batch number is required",
          }),
        is_final_batch: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .required()
          .messages({
            "alternatives.match": "is_final_batch must be a boolean (true/false) or string ('true'/'false')",
            "any.required": "is_final_batch flag is required",
          }),
      })
      .unknown(true), // Allow file uploads and other fields
  });

/**
 * Factory function to get the appropriate validator based on batch type
 * @param type - The batch request type ('basic-info', 'ingredients-nutrition', 'steps', 'uploads')
 * @returns The appropriate Celebrate validator middleware
 */
export const validateBatchRequest = (type: string) => {
  switch (type) {
    case 'basic-info':
      return validateBasicInfoBatch();
    case 'ingredients-nutrition':
      return validateIngredientsNutritionBatch();
    case 'steps':
      return validateStepsBatch();
    case 'uploads':
      return validateUploadsBatch();
    default:
      throw new Error(`Unknown batch request type: ${type}`);
  }
};

// Export individual validators for direct use if needed
export {
  validateBasicInfoBatch,
  validateIngredientsNutritionBatch,
  validateStepsBatch,
  validateUploadsBatch,
};
