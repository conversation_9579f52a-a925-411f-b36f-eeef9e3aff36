<?xml version="1.0" ?>
<!DOCTYPE coverage SYSTEM "http://cobertura.sourceforge.net/xml/coverage-04.dtd">
<coverage lines-valid="301" lines-covered="0" line-rate="0" branches-valid="30" branches-covered="0" branch-rate="0" timestamp="1751266468279" complexity="0" version="0.1">
  <sources>
    <source>e:\Recipe-ms\backend-recipes-ms</source>
  </sources>
  <packages>
    <package name="backend-recipes-ms" line-rate="0" branch-rate="1">
      <classes>
        <class name=".eslintrc.js" filename=".eslintrc.js" line-rate="0" branch-rate="1">
          <methods>
          </methods>
          <lines>
            <line number="1" hits="0" branch="false"/>
          </lines>
        </class>
      </classes>
    </package>
    <package name="backend-recipes-ms.shared.config" line-rate="1" branch-rate="1">
      <classes>
        <class name="index.ts" filename="shared\config\index.ts" line-rate="1" branch-rate="1">
          <methods>
          </methods>
          <lines>
          </lines>
        </class>
      </classes>
    </package>
    <package name="backend-recipes-ms.src.helper" line-rate="0" branch-rate="1">
      <classes>
        <class name="constant.ts" filename="src\helper\constant.ts" line-rate="0" branch-rate="1">
          <methods>
          </methods>
          <lines>
            <line number="4" hits="0" branch="false"/>
            <line number="26" hits="0" branch="false"/>
            <line number="38" hits="0" branch="false"/>
            <line number="55" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="i18n.ts" filename="src\helper\i18n.ts" line-rate="0" branch-rate="1">
          <methods>
          </methods>
          <lines>
            <line number="4" hits="0" branch="false"/>
          </lines>
        </class>
      </classes>
    </package>
    <package name="backend-recipes-ms.src.rabbitmq" line-rate="0" branch-rate="1">
      <classes>
        <class name="consumerQueue.ts" filename="src\rabbitmq\consumerQueue.ts" line-rate="0" branch-rate="1">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="4" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="4" hits="0" branch="false"/>
            <line number="5" hits="0" branch="false"/>
            <line number="6" hits="0" branch="false"/>
            <line number="18" hits="0" branch="false"/>
            <line number="20" hits="0" branch="false"/>
          </lines>
        </class>
      </classes>
    </package>
    <package name="backend-recipes-ms.src.routes" line-rate="1" branch-rate="1">
      <classes>
        <class name="index.ts" filename="src\routes\index.ts" line-rate="1" branch-rate="1">
          <methods>
          </methods>
          <lines>
          </lines>
        </class>
      </classes>
    </package>
    <package name="backend-recipes-ms.src.routes.private" line-rate="0" branch-rate="0">
      <classes>
        <class name="analytics.routes.ts" filename="src\routes\private\analytics.routes.ts" line-rate="0" branch-rate="1">
          <methods>
          </methods>
          <lines>
            <line number="5" hits="0" branch="false"/>
            <line number="110" hits="0" branch="false"/>
            <line number="176" hits="0" branch="false"/>
            <line number="317" hits="0" branch="false"/>
            <line number="327" hits="0" branch="false"/>
            <line number="337" hits="0" branch="false"/>
            <line number="377" hits="0" branch="false"/>
            <line number="466" hits="0" branch="false"/>
            <line number="539" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="category.routes.ts" filename="src\routes\private\category.routes.ts" line-rate="0" branch-rate="0">
          <methods>
          </methods>
          <lines>
            <line number="7" hits="0" branch="false"/>
            <line number="12" hits="0" branch="false"/>
            <line number="128" hits="0" branch="false"/>
            <line number="183" hits="0" branch="false"/>
            <line number="264" hits="0" branch="false"/>
            <line number="357" hits="0" branch="false"/>
            <line number="415" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="dashboard.routes.ts" filename="src\routes\private\dashboard.routes.ts" line-rate="0" branch-rate="1">
          <methods>
          </methods>
          <lines>
            <line number="5" hits="0" branch="false"/>
            <line number="89" hits="0" branch="false"/>
            <line number="131" hits="0" branch="false"/>
            <line number="186" hits="0" branch="false"/>
            <line number="239" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="foodAttributes.routes.ts" filename="src\routes\private\foodAttributes.routes.ts" line-rate="0" branch-rate="0">
          <methods>
          </methods>
          <lines>
            <line number="7" hits="0" branch="false"/>
            <line number="12" hits="0" branch="false"/>
            <line number="138" hits="0" branch="false"/>
            <line number="193" hits="0" branch="false"/>
            <line number="274" hits="0" branch="false"/>
            <line number="367" hits="0" branch="false"/>
            <line number="425" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="recipe-batch.routes.ts" filename="src\routes\private\recipe-batch.routes.ts" line-rate="0" branch-rate="1">
          <methods>
          </methods>
          <lines>
            <line number="7" hits="0" branch="false"/>
            <line number="10" hits="0" branch="false"/>
            <line number="17" hits="0" branch="false"/>
            <line number="24" hits="0" branch="false"/>
            <line number="31" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="recipe.routes.ts" filename="src\routes\private\recipe.routes.ts" line-rate="0" branch-rate="0">
          <methods>
          </methods>
          <lines>
            <line number="8" hits="0" branch="false"/>
            <line number="13" hits="0" branch="false"/>
            <line number="230" hits="0" branch="false"/>
            <line number="306" hits="0" branch="false"/>
            <line number="527" hits="0" branch="false"/>
            <line number="756" hits="0" branch="false"/>
            <line number="844" hits="0" branch="false"/>
            <line number="930" hits="0" branch="false"/>
            <line number="986" hits="0" branch="false"/>
            <line number="1044" hits="0" branch="false"/>
            <line number="1155" hits="0" branch="false"/>
            <line number="1214" hits="0" branch="false"/>
            <line number="1274" hits="0" branch="false"/>
            <line number="1343" hits="0" branch="false"/>
            <line number="1416" hits="0" branch="false"/>
            <line number="1585" hits="0" branch="false"/>
            <line number="1729" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="recipeMeasure.routes.ts" filename="src\routes\private\recipeMeasure.routes.ts" line-rate="0" branch-rate="0">
          <methods>
          </methods>
          <lines>
            <line number="8" hits="0" branch="false"/>
            <line number="13" hits="0" branch="false"/>
            <line number="120" hits="0" branch="false"/>
            <line number="175" hits="0" branch="false"/>
            <line number="248" hits="0" branch="false"/>
            <line number="334" hits="0" branch="false"/>
            <line number="392" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="settings.routes.ts" filename="src\routes\private\settings.routes.ts" line-rate="0" branch-rate="1">
          <methods>
          </methods>
          <lines>
            <line number="5" hits="0" branch="false"/>
            <line number="59" hits="0" branch="false"/>
            <line number="83" hits="0" branch="false"/>
          </lines>
        </class>
      </classes>
    </package>
    <package name="backend-recipes-ms.src.routes.public" line-rate="0" branch-rate="1">
      <classes>
        <class name="analytics.routes.ts" filename="src\routes\public\analytics.routes.ts" line-rate="0" branch-rate="1">
          <methods>
          </methods>
          <lines>
            <line number="5" hits="0" branch="false"/>
            <line number="164" hits="0" branch="false"/>
            <line number="220" hits="0" branch="false"/>
            <line number="300" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="recipe.routes.ts" filename="src\routes\public\recipe.routes.ts" line-rate="0" branch-rate="1">
          <methods>
          </methods>
          <lines>
            <line number="5" hits="0" branch="false"/>
            <line number="189" hits="0" branch="false"/>
            <line number="243" hits="0" branch="false"/>
            <line number="304" hits="0" branch="false"/>
          </lines>
        </class>
      </classes>
    </package>
    <package name="backend-recipes-ms.src.seeders" line-rate="0" branch-rate="0">
      <classes>
        <class name="20250616100000-master_recipe_data_seeder.js" filename="src\seeders\20250616100000-master_recipe_data_seeder.js" line-rate="0" branch-rate="0">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="5" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="64" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="0" branch="false"/>
            <line number="4" hits="0" branch="false"/>
            <line number="6" hits="0" branch="false"/>
            <line number="8" hits="0" branch="false"/>
            <line number="10" hits="0" branch="false"/>
            <line number="21" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="23" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="24" hits="0" branch="false"/>
            <line number="25" hits="0" branch="false"/>
            <line number="26" hits="0" branch="false"/>
            <line number="29" hits="0" branch="false"/>
            <line number="30" hits="0" branch="false"/>
            <line number="31" hits="0" branch="false"/>
            <line number="32" hits="0" branch="false"/>
            <line number="33" hits="0" branch="false"/>
            <line number="34" hits="0" branch="false"/>
            <line number="35" hits="0" branch="false"/>
            <line number="36" hits="0" branch="false"/>
            <line number="37" hits="0" branch="false"/>
            <line number="42" hits="0" branch="false"/>
            <line number="43" hits="0" branch="false"/>
            <line number="44" hits="0" branch="false"/>
            <line number="45" hits="0" branch="false"/>
            <line number="46" hits="0" branch="false"/>
            <line number="47" hits="0" branch="false"/>
            <line number="48" hits="0" branch="false"/>
            <line number="49" hits="0" branch="false"/>
            <line number="50" hits="0" branch="false"/>
            <line number="51" hits="0" branch="false"/>
            <line number="52" hits="0" branch="false"/>
            <line number="53" hits="0" branch="false"/>
            <line number="54" hits="0" branch="false"/>
            <line number="55" hits="0" branch="false"/>
            <line number="56" hits="0" branch="false"/>
            <line number="59" hits="0" branch="false"/>
            <line number="60" hits="0" branch="false"/>
            <line number="65" hits="0" branch="false"/>
            <line number="67" hits="0" branch="false"/>
            <line number="69" hits="0" branch="false"/>
            <line number="74" hits="0" branch="false"/>
            <line number="79" hits="0" branch="false"/>
            <line number="84" hits="0" branch="false"/>
            <line number="86" hits="0" branch="false"/>
            <line number="87" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="20250616100001-mo_categories.js" filename="src\seeders\20250616100001-mo_categories.js" line-rate="0" branch-rate="0">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="5" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="50" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_2)" hits="0" signature="()V">
              <lines>
                <line number="71" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="0" branch="false"/>
            <line number="4" hits="0" branch="false"/>
            <line number="7" hits="0" branch="false"/>
            <line number="12" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="14" hits="0" branch="false"/>
            <line number="29" hits="0" branch="false"/>
            <line number="47" hits="0" branch="false"/>
            <line number="50" hits="0" branch="false"/>
            <line number="64" hits="0" branch="false"/>
            <line number="65" hits="0" branch="false"/>
            <line number="67" hits="0" branch="false"/>
            <line number="72" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="20250616100002-mo_food_attributes.js" filename="src\seeders\20250616100002-mo_food_attributes.js" line-rate="0" branch-rate="0">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="5" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="169" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_2)" hits="0" signature="()V">
              <lines>
                <line number="191" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="0" branch="false"/>
            <line number="4" hits="0" branch="false"/>
            <line number="7" hits="0" branch="false"/>
            <line number="12" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="14" hits="0" branch="false"/>
            <line number="102" hits="0" branch="false"/>
            <line number="166" hits="0" branch="false"/>
            <line number="169" hits="0" branch="false"/>
            <line number="184" hits="0" branch="false"/>
            <line number="185" hits="0" branch="false"/>
            <line number="187" hits="0" branch="false"/>
            <line number="192" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="20250616100003-mo_cuisine_attributes.js" filename="src\seeders\20250616100003-mo_cuisine_attributes.js" line-rate="0" branch-rate="0">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="5" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="93" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_2)" hits="0" signature="()V">
              <lines>
                <line number="115" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="0" branch="false"/>
            <line number="4" hits="0" branch="false"/>
            <line number="7" hits="0" branch="false"/>
            <line number="12" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="14" hits="0" branch="false"/>
            <line number="93" hits="0" branch="false"/>
            <line number="108" hits="0" branch="false"/>
            <line number="109" hits="0" branch="false"/>
            <line number="111" hits="0" branch="false"/>
            <line number="116" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="20250616100004-mo_recipe_measure.js" filename="src\seeders\20250616100004-mo_recipe_measure.js" line-rate="0" branch-rate="0">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="5" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="43" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_2)" hits="0" signature="()V">
              <lines>
                <line number="63" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="0" branch="false"/>
            <line number="4" hits="0" branch="false"/>
            <line number="7" hits="0" branch="false"/>
            <line number="12" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="14" hits="0" branch="false"/>
            <line number="43" hits="0" branch="false"/>
            <line number="56" hits="0" branch="false"/>
            <line number="57" hits="0" branch="false"/>
            <line number="59" hits="0" branch="false"/>
            <line number="64" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="20250616100005-mo_nutrition_attributes.js" filename="src\seeders\20250616100005-mo_nutrition_attributes.js" line-rate="0" branch-rate="0">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="5" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="210" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_2)" hits="0" signature="()V">
              <lines>
                <line number="232" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="0" branch="false"/>
            <line number="4" hits="0" branch="false"/>
            <line number="7" hits="0" branch="false"/>
            <line number="12" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="14" hits="0" branch="false"/>
            <line number="210" hits="0" branch="false"/>
            <line number="225" hits="0" branch="false"/>
            <line number="226" hits="0" branch="false"/>
            <line number="228" hits="0" branch="false"/>
            <line number="233" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="20250616100006-mo_haccp_attributes.js" filename="src\seeders\20250616100006-mo_haccp_attributes.js" line-rate="0" branch-rate="0">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="5" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="69" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_2)" hits="0" signature="()V">
              <lines>
                <line number="91" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="0" branch="false"/>
            <line number="4" hits="0" branch="false"/>
            <line number="7" hits="0" branch="false"/>
            <line number="12" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="15" hits="0" branch="false"/>
            <line number="69" hits="0" branch="false"/>
            <line number="84" hits="0" branch="false"/>
            <line number="85" hits="0" branch="false"/>
            <line number="87" hits="0" branch="false"/>
            <line number="92" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="20250616100007-mo_cooking_preparation_methods.js" filename="src\seeders\20250616100007-mo_cooking_preparation_methods.js" line-rate="0" branch-rate="0">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="5" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="212" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_2)" hits="0" signature="()V">
              <lines>
                <line number="234" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="1" hits="0" branch="false"/>
            <line number="4" hits="0" branch="false"/>
            <line number="7" hits="0" branch="false"/>
            <line number="12" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="15" hits="0" branch="false"/>
            <line number="67" hits="0" branch="false"/>
            <line number="209" hits="0" branch="false"/>
            <line number="212" hits="0" branch="false"/>
            <line number="227" hits="0" branch="false"/>
            <line number="228" hits="0" branch="false"/>
            <line number="230" hits="0" branch="false"/>
            <line number="235" hits="0" branch="false"/>
          </lines>
        </class>
      </classes>
    </package>
    <package name="backend-recipes-ms.src.swagger" line-rate="0" branch-rate="1">
      <classes>
        <class name="swagger.config.ts" filename="src\swagger\swagger.config.ts" line-rate="0" branch-rate="1">
          <methods>
          </methods>
          <lines>
            <line number="3" hits="0" branch="false"/>
            <line number="1005" hits="0" branch="false"/>
          </lines>
        </class>
      </classes>
    </package>
    <package name="backend-recipes-ms.src.validators" line-rate="0" branch-rate="0">
      <classes>
        <class name="analytics.validator.ts" filename="src\validators\analytics.validator.ts" line-rate="0" branch-rate="1">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="36" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="48" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_2)" hits="0" signature="()V">
              <lines>
                <line number="64" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_3)" hits="0" signature="()V">
              <lines>
                <line number="84" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_4)" hits="0" signature="()V">
              <lines>
                <line number="118" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_5)" hits="0" signature="()V">
              <lines>
                <line number="156" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_6)" hits="0" signature="()V">
              <lines>
                <line number="192" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_7)" hits="0" signature="()V">
              <lines>
                <line number="203" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_8)" hits="0" signature="()V">
              <lines>
                <line number="236" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_9)" hits="0" signature="()V">
              <lines>
                <line number="260" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_10)" hits="0" signature="()V">
              <lines>
                <line number="274" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="8" hits="0" branch="false"/>
            <line number="15" hits="0" branch="false"/>
            <line number="36" hits="0" branch="false"/>
            <line number="37" hits="0" branch="false"/>
            <line number="48" hits="0" branch="false"/>
            <line number="49" hits="0" branch="false"/>
            <line number="64" hits="0" branch="false"/>
            <line number="65" hits="0" branch="false"/>
            <line number="84" hits="0" branch="false"/>
            <line number="85" hits="0" branch="false"/>
            <line number="118" hits="0" branch="false"/>
            <line number="119" hits="0" branch="false"/>
            <line number="156" hits="0" branch="false"/>
            <line number="157" hits="0" branch="false"/>
            <line number="192" hits="0" branch="false"/>
            <line number="193" hits="0" branch="false"/>
            <line number="203" hits="0" branch="false"/>
            <line number="204" hits="0" branch="false"/>
            <line number="236" hits="0" branch="false"/>
            <line number="237" hits="0" branch="false"/>
            <line number="260" hits="0" branch="false"/>
            <line number="261" hits="0" branch="false"/>
            <line number="274" hits="0" branch="false"/>
            <line number="275" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="category.validator.ts" filename="src\validators\category.validator.ts" line-rate="0" branch-rate="1">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="4" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="23" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_2)" hits="0" signature="()V">
              <lines>
                <line number="45" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_3)" hits="0" signature="()V">
              <lines>
                <line number="52" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_4)" hits="0" signature="()V">
              <lines>
                <line number="59" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="4" hits="0" branch="false"/>
            <line number="5" hits="0" branch="false"/>
            <line number="23" hits="0" branch="false"/>
            <line number="24" hits="0" branch="false"/>
            <line number="45" hits="0" branch="false"/>
            <line number="46" hits="0" branch="false"/>
            <line number="52" hits="0" branch="false"/>
            <line number="53" hits="0" branch="false"/>
            <line number="59" hits="0" branch="false"/>
            <line number="60" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="contact.validator.ts" filename="src\validators\contact.validator.ts" line-rate="0" branch-rate="1">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="3" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="18" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_2)" hits="0" signature="()V">
              <lines>
                <line number="25" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_3)" hits="0" signature="()V">
              <lines>
                <line number="39" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_4)" hits="0" signature="()V">
              <lines>
                <line number="46" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_5)" hits="0" signature="()V">
              <lines>
                <line number="53" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="3" hits="0" branch="false"/>
            <line number="4" hits="0" branch="false"/>
            <line number="18" hits="0" branch="false"/>
            <line number="25" hits="0" branch="false"/>
            <line number="26" hits="0" branch="false"/>
            <line number="39" hits="0" branch="false"/>
            <line number="40" hits="0" branch="false"/>
            <line number="46" hits="0" branch="false"/>
            <line number="47" hits="0" branch="false"/>
            <line number="53" hits="0" branch="false"/>
            <line number="54" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="dashboard.validator.ts" filename="src\validators\dashboard.validator.ts" line-rate="0" branch-rate="1">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="20" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="37" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_2)" hits="0" signature="()V">
              <lines>
                <line number="55" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_3)" hits="0" signature="()V">
              <lines>
                <line number="85" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="4" hits="0" branch="false"/>
            <line number="20" hits="0" branch="false"/>
            <line number="21" hits="0" branch="false"/>
            <line number="37" hits="0" branch="false"/>
            <line number="38" hits="0" branch="false"/>
            <line number="55" hits="0" branch="false"/>
            <line number="56" hits="0" branch="false"/>
            <line number="85" hits="0" branch="false"/>
            <line number="86" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="foodAttributes.validator.ts" filename="src\validators\foodAttributes.validator.ts" line-rate="0" branch-rate="1">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="4" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="23" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_2)" hits="0" signature="()V">
              <lines>
                <line number="45" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_3)" hits="0" signature="()V">
              <lines>
                <line number="52" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_4)" hits="0" signature="()V">
              <lines>
                <line number="59" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="4" hits="0" branch="false"/>
            <line number="5" hits="0" branch="false"/>
            <line number="23" hits="0" branch="false"/>
            <line number="24" hits="0" branch="false"/>
            <line number="45" hits="0" branch="false"/>
            <line number="46" hits="0" branch="false"/>
            <line number="52" hits="0" branch="false"/>
            <line number="53" hits="0" branch="false"/>
            <line number="59" hits="0" branch="false"/>
            <line number="60" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="ingredients.validator.ts" filename="src\validators\ingredients.validator.ts" line-rate="0" branch-rate="0">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="3" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="124" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_2)" hits="0" signature="()V">
              <lines>
                <line number="144" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_3)" hits="0" signature="()V">
              <lines>
                <line number="257" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_4)" hits="0" signature="()V">
              <lines>
                <line number="277" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_5)" hits="0" signature="()V">
              <lines>
                <line number="287" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_6)" hits="0" signature="()V">
              <lines>
                <line number="297" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_7)" hits="0" signature="()V">
              <lines>
                <line number="356" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="3" hits="0" branch="false"/>
            <line number="4" hits="0" branch="false"/>
            <line number="126" hits="0" branch="false"/>
            <line number="127" hits="0" branch="false"/>
            <line number="128" hits="0" branch="false"/>
            <line number="129" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="130" hits="0" branch="false"/>
            <line number="134" hits="0" branch="false"/>
            <line number="136" hits="0" branch="false"/>
            <line number="144" hits="0" branch="false"/>
            <line number="145" hits="0" branch="false"/>
            <line number="259" hits="0" branch="false"/>
            <line number="260" hits="0" branch="false"/>
            <line number="261" hits="0" branch="false"/>
            <line number="262" hits="0" branch="true" condition-coverage="0% (0/2)"/>
            <line number="263" hits="0" branch="false"/>
            <line number="267" hits="0" branch="false"/>
            <line number="269" hits="0" branch="false"/>
            <line number="277" hits="0" branch="false"/>
            <line number="278" hits="0" branch="false"/>
            <line number="287" hits="0" branch="false"/>
            <line number="288" hits="0" branch="false"/>
            <line number="297" hits="0" branch="false"/>
            <line number="298" hits="0" branch="false"/>
            <line number="356" hits="0" branch="false"/>
            <line number="357" hits="0" branch="false"/>
          </lines>
        </class>
        <class name="recipeMeasure.validator.ts" filename="src\validators\recipeMeasure.validator.ts" line-rate="0" branch-rate="1">
          <methods>
            <method name="(anonymous_0)" hits="0" signature="()V">
              <lines>
                <line number="4" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_1)" hits="0" signature="()V">
              <lines>
                <line number="20" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_2)" hits="0" signature="()V">
              <lines>
                <line number="39" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_3)" hits="0" signature="()V">
              <lines>
                <line number="46" hits="0"/>
              </lines>
            </method>
            <method name="(anonymous_4)" hits="0" signature="()V">
              <lines>
                <line number="53" hits="0"/>
              </lines>
            </method>
          </methods>
          <lines>
            <line number="4" hits="0" branch="false"/>
            <line number="5" hits="0" branch="false"/>
            <line number="20" hits="0" branch="false"/>
            <line number="21" hits="0" branch="false"/>
            <line number="39" hits="0" branch="false"/>
            <line number="40" hits="0" branch="false"/>
            <line number="46" hits="0" branch="false"/>
            <line number="47" hits="0" branch="false"/>
            <line number="53" hits="0" branch="false"/>
            <line number="54" hits="0" branch="false"/>
          </lines>
        </class>
      </classes>
    </package>
  </packages>
</coverage>
