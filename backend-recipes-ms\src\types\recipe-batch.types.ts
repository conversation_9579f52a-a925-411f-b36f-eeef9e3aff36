export interface RecipeBatchProgress {
  recipe_id: number;
  basic_info_completed: boolean;
  ingredients_nutrition_cuisine_completed: boolean;
  steps_batches_completed: number;
  steps_total_batches: number;
  uploads_batches_completed: number;
  uploads_total_batches: number;
  is_fully_completed: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface RecipeBasicInfoData {
  recipe_title: string;
  recipe_public_title?: string;
  recipe_description?: string;
  recipe_preparation_time?: number;
  recipe_cook_time?: number;
  has_recipe_public_visibility?: boolean;
  has_recipe_private_visibility?: boolean;
  recipe_status?: string;
  recipe_serve_in?: string;
  recipe_complexity_level?: string;
  recipe_garnish?: string;
  recipe_head_chef_tips?: string;
  recipe_foh_tips?: string;
  recipe_impression?: number;
  recipe_yield?: number;
  recipe_yield_unit?: string;
  recipe_total_portions?: number;
  recipe_single_portion_size?: number;
  recipe_serving_method?: string;
  recipe_placeholder?: string;
  is_ingredient_cooking_method?: boolean;
  is_preparation_method?: boolean;
  is_cost_manual?: boolean;
  categories?: number[];
}

export interface RecipeIngredientsNutritionCuisineData {
  recipe_id: number;
  ingredients?: Array<{
    id: number;
    quantity?: number;
    measure?: number;
    wastage?: number;
    cost?: number;
    cooking_method?: number;
    preparation_method?: number;
  }>;
  nutrition_attributes?: Array<{
    id: number;
    unit_of_measure?: string;
    unit?: number;
    attribute_description?: string;
    use_default?: boolean;
  }>;
  allergen_attributes?: {
    contains?: number[];
    may_contain?: number[];
  };
  cuisine_attributes?: number[];
  dietary_attributes?: number[];
  haccp_attributes?: Array<{
    id: number;
    attribute_description?: string;
    use_default?: boolean;
  }>;

}

export interface RecipeStepsData {
  recipe_id: number;
  steps: Array<{
    step_title?: string;
    step_description: string;
    step_note?: string;
    step_warning?: string;
    step_time?: number;
    step_temperature?: number;
    step_temperature_unit?: string;
  }>;
  batch_number: number;
  is_final_batch: boolean;
}

export interface RecipeUploadsData {
  recipe_id: number;
  batch_number: number;
  is_final_batch: boolean;
  // files will come from multer middleware
}

export interface RecipeStepsBatchData {
  recipe_id: number;
  batch_number: number;
  total_batches: number;
  steps: Array<{
    order: number;
    description?: string;
    item_id?: number;
  }>;
}

export interface RecipeUploadsBatchData {
  recipe_id: number;
  batch_number: number;
  total_batches: number;
  upload_type: 'recipe_files' | 'step_images' | 'recipe_placeholder';
  resources?: Array<{
    type: string;
    item_id?: number;
    item_link?: string;
    item_link_type?: string;
  }>;
}

export interface BatchProcessingResult {
  success: boolean;
  batch_number: number;
  total_batches: number;
  processed_items: number;
  errors?: string[];
  recipe_id: number;
}

export interface RecipeCreationProgress {
  recipe_id: number;
  current_step: 'basic_info' | 'ingredients_nutrition_cuisine' | 'steps' | 'uploads' | 'completed';
  steps_progress: {
    completed_batches: number;
    total_batches: number;
    percentage: number;
  };
  uploads_progress: {
    completed_batches: number;
    total_batches: number;
    percentage: number;
  };
  overall_percentage: number;
}

export const BATCH_SIZES = {
  STEPS_PER_BATCH: 5,
  UPLOADS_PER_BATCH: 5,
} as const;

export const RECIPE_CREATION_STEPS = {
  BASIC_INFO: 'basic_info',
  INGREDIENTS_NUTRITION_CUISINE: 'ingredients_nutrition_cuisine',
  STEPS: 'steps',
  UPLOADS: 'uploads',
  COMPLETED: 'completed',
} as const;
