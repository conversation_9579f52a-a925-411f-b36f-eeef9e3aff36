import express from 'express';
import recipeBatchController from '../../controller/recipe-batch.controller';
import auth from '../../middleware/auth';
import upload from '../../middleware/upload';
import { validateBatchRequest } from '../../middleware/batch-validator';

const router = express.Router();

// Basic recipe information API
router.post('/basic-info',
  validateBatchRequest('basic-info'),
  recipeBatchController.createRecipeBasicInfo
);

// Ingredients, nutrition, and cuisine data API
router.post('/ingredients-nutrition',
  auth,
  validateBatchRequest('ingredients-nutrition'),
  recipeBatchController.addIngredientsNutritionCuisine
);

// Recipe steps batch API
router.post('/steps',
  auth,
  validateBatchRequest('steps'),
  recipeBatchController.addRecipeSteps
);

// Recipe uploads batch API
router.post('/uploads',
  auth,
  validateBatchRequest('uploads'),
  upload.array('files', 5),
  recipeBatchController.addRecipeUploads
);

export default router;
