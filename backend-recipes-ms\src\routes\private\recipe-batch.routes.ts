import express from 'express';
import recipeBatchController from '../../controller/recipe-batch.controller';
import auth from '../../middleware/auth';
import upload from '../../middleware/upload';
import batchValidator from '../../validators/batch.validator';

const router = express.Router();

// Basic recipe information API
router.post('/basic-info',
  batchValidator.validateBasicInfoBatch(),
  recipeBatchController.createRecipeBasicInfo
);

// Ingredients, nutrition, and cuisine data API
router.post('/ingredients-nutrition',
  auth,
  batchValidator.validateIngredientsNutritionBatch(),
  recipeBatchController.addIngredientsNutritionCuisine
);

// Recipe steps batch API
router.post('/steps',
  auth,
  batchValidator.validateStepsBatch(),
  recipeBatchController.addRecipeSteps
);

// Recipe uploads batch API
router.post('/uploads',
  auth,
  batchValidator.validateUploadsBatch(),
  upload.array('files', 5),
  recipeBatchController.addRecipeUploads
);

export default router;
